"use client"

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, LayoutDashboard, Loader2, KeyRound, LogIn } from 'lucide-react';
import { toast } from 'sonner';
import { Alert, AlertDescription } from '@/components/ui/alert';

const keyAuthSchema = z.object({
  accessKey: z.string()
    .min(1, { message: 'Access key is required' })
    .refine((key) => {
      // New format: 8 words + 10 digits
      const newFormat = /^[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-\d{10}$/.test(key);
      // Legacy format: 2 words + 2 digits
      const legacyFormat = /^[a-z]+-[a-z]+-\d{2}$/.test(key);
      return newFormat || legacyFormat;
    }, 'Invalid access key format. Expected: 8-words-10digits or legacy 2-words-2digits'),
});

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const router = useRouter();

  const form = useForm<z.infer<typeof keyAuthSchema>>({
    resolver: zodResolver(keyAuthSchema),
    defaultValues: {
      accessKey: '',
    },
  });

  async function onSubmit(values: z.infer<typeof keyAuthSchema>) {
    setIsLoading(true);
    setErrorDetails(null);

    try {
      const requestBody = {
        accessKey: values.accessKey.toLowerCase().trim()
      };

      // Make the authentication request
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        credentials: 'include',
      });

      // Get the response text
      const responseText = await response.text();

      // Parse the JSON response
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        setErrorDetails('Invalid response from server. Please try again later.');
        throw new Error('Server returned an invalid response');
      }

      // Check if login was successful
      if (!response.ok || !data.success) {
        const errorMessage = data.error?.message || 'Unknown error';
        const errorCode = data.error?.code || 'UNKNOWN_ERROR';

        // Provide user-friendly error messages
        if (errorCode === 'INVALID_CREDENTIALS' || errorCode === 'USER_NOT_FOUND') {
          setErrorDetails('Invalid access key. Please check your key and try again.');
          throw new Error('Invalid access key');
        } else if (errorCode === 'DATABASE_ERROR') {
          setErrorDetails('Database connection error. Please try again later.');
          throw new Error('Database connection failed');
        } else {
          setErrorDetails(`Authentication failed: ${errorMessage}`);
          throw new Error(errorMessage || 'Authentication failed');
        }
      }

      // Extract user data and token
      const responseData = data.data || {};
      const token = responseData.token;
      const user = responseData.user;

      if (!token || !user) {
        setErrorDetails('Server response is missing required data');
        throw new Error('Invalid server response');
      }

      // Store in localStorage for PWA support and offline access
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify({
        id: user.id,
        name: user.name,
        email: user.email,
        lastLogin: new Date().toISOString()
      }));

      // Also store the complete profile
      localStorage.setItem('userProfile', JSON.stringify(user));

      // --- Fix: Clear categories cache/localStorage so next page fetches fresh categories ---
      localStorage.removeItem('categories');
      // --------------------------------------------------------------

      toast.success('Logged in successfully');
      router.push('/home');
    } catch (error: any) {
      toast.error(error.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <Link href="/" className="absolute left-4 top-4 md:left-8 md:top-8 flex items-center gap-2 font-bold text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">
        <LayoutDashboard className="h-5 w-5" />
        <span>NoteHour</span>
      </Link>

      <Card className="w-full max-w-md shadow-xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
        <CardHeader className="text-center pb-6">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30">
            <LogIn className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
            Welcome Back
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-400 mt-2">
            Enter your access key to sign in to your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {errorDetails && (
                <Alert variant="destructive" className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="mt-2">
                    <div className="text-sm font-medium">Sign In Error:</div>
                    <div className="text-xs mt-1 break-words">{errorDetails}</div>
                  </AlertDescription>
                </Alert>
              )}

              <FormField
                control={form.control}
                name="accessKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700 dark:text-gray-300 font-medium">
                      Access Key
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <KeyRound className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="your-access-key-here"
                          {...field}
                          className="pl-10 font-mono text-sm bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400"
                          autoComplete="off"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-2.5 transition-all duration-200 shadow-lg hover:shadow-xl"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing In...
                  </>
                ) : (
                  <>
                    <LogIn className="mr-2 h-4 w-4" />
                    Sign In
                  </>
                )}
              </Button>

              <div className="text-center">
                <Link
                  href="/auth/recovery"
                  className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 underline-offset-4 hover:underline transition-colors"
                >
                  Lost your access key? Use recovery code
                </Link>
              </div>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col items-center gap-3 pt-6">
          <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
            Need an access key?{' '}
            <Link href="/auth/register" className="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 underline-offset-4 hover:underline transition-colors">
              Generate Key
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}