"use client"

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { LayoutDashboard, Loader2, KeyRound, UserPlus, Download, Copy, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';

// No schema needed for registration - system generates access key automatically
const registerSchema = z.object({});

export default function RegisterPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [generatedKey, setGeneratedKey] = useState<string | null>(null);
  const [userName, setUserName] = useState('');
  const [isRegistered, setIsRegistered] = useState(false);
  const [recoveryCodes, setRecoveryCodes] = useState<string[]>([]);
  const [showRecoveryCodes, setShowRecoveryCodes] = useState(false);
  const router = useRouter();

  const form = useForm<z.infer<typeof registerSchema>>({
    resolver: zodResolver(registerSchema),
    defaultValues: {},
  });

  async function generateNewKey() {
    if (isLoading) {
      console.log('Generation already in progress, ignoring duplicate request');
      return;
    }

    setIsLoading(true);

    try {
      console.log('Generating new access key...');

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: 'generate'
        }),
      });

      const data = await response.json();
      console.log('Key generation response:', { status: response.status, data });

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Key generation failed';
        throw new Error(errorMessage);
      }

      // Handle both possible response structures
      const accessKey = data.data?.accessKey || data.accessKey;
      if (!accessKey) {
        console.error('No access key in response:', data);
        throw new Error('No access key received from server');
      }

      console.log('Generated new access key:', accessKey);
      setGeneratedKey(accessKey);
      setIsRegistered(false); // Reset registration status
      toast.success('New access key generated!');
    } catch (error: any) {
      console.error('Key generation error:', error);
      toast.error(error.message || 'Failed to generate access key');
    } finally {
      setIsLoading(false);
    }
  }

  async function saveKey() {
    if (!generatedKey) {
      toast.error('No access key to save');
      return;
    }

    if (isSaving) {
      console.log('Save already in progress, ignoring duplicate request');
      return;
    }

    setIsSaving(true);

    try {
      console.log('Saving access key:', generatedKey);

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: 'save',
          accessKey: generatedKey,
          name: userName.trim(),
        }),
      });

      const data = await response.json();
      console.log('Save key response:', {
        status: response.status,
        data,
        accessKey: generatedKey
      });

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 409) {
          // Conflict - key already exists
          console.error('Access key already exists, generating new one...');
          toast.error('This access key is no longer available. Generating a new one...');
          // Generate a new key automatically
          await generateNewKey();
          return;
        }

        const errorMessage = data.error?.message || data.message || 'Failed to save access key';
        throw new Error(errorMessage);
      }

      // Store recovery codes if provided - handle correct response structure
      const responseData = data.data || data;
      if (responseData.recoveryCodes) {
        setRecoveryCodes(responseData.recoveryCodes);
        setShowRecoveryCodes(true);
      }

      setIsRegistered(true);
      toast.success('Account created successfully! Please save your recovery codes.');
    } catch (error: any) {
      console.error('Save key error:', error);

      // If it's a duplicate key error, try to generate a new key
      if (error.message.includes('already exists') || error.message.includes('User already exists')) {
        console.log('Key conflict detected, generating new key...');
        toast.error('This access key is no longer available. Generating a new one...');
        await generateNewKey();
      } else {
        toast.error(error.message || 'Failed to save access key');
      }
    } finally {
      setIsSaving(false);
    }
  }

  async function onSubmit(values: z.infer<typeof registerSchema>) {
    generateNewKey();
  }

  const copyToClipboard = async () => {
    if (generatedKey) {
      try {
        await navigator.clipboard.writeText(generatedKey);
        toast.success('Access key copied to clipboard!');
      } catch (error) {
        toast.error('Failed to copy to clipboard');
      }
    }
  };

  const goToLogin = () => {
    router.push('/auth/login');
  };

  const copyRecoveryCodes = async () => {
    if (recoveryCodes.length > 0) {
      try {
        const codesText = recoveryCodes.join('\n');
        await navigator.clipboard.writeText(codesText);
        toast.success('Recovery codes copied to clipboard!');
      } catch (error) {
        toast.error('Failed to copy recovery codes');
      }
    }
  };

  const downloadRecoveryCodes = () => {
    if (recoveryCodes.length > 0) {
      const codesText = `NoteHour Recovery Codes\n\nAccess Key: ${generatedKey}\n\nRecovery Codes:\n${recoveryCodes.join('\n')}\n\nIMPORTANT:\n- Save these codes in a secure location\n- Each code can only be used once\n- You'll need these if you lose access to your account\n- Keep them separate from your access key`;

      const blob = new Blob([codesText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `notehour-recovery-codes-${Date.now()}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Recovery codes downloaded!');
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-900 dark:to-gray-800">
      <Link href="/" className="absolute left-4 top-4 md:left-8 md:top-8 flex items-center gap-2 font-bold text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">
        <LayoutDashboard className="h-5 w-5" />
        <span>NoteHour</span>
      </Link>

      <Card className="w-full max-w-md shadow-xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
        <CardHeader className="text-center pb-6">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30">
            {isRegistered ? (
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            ) : generatedKey ? (
              <KeyRound className="h-6 w-6 text-green-600 dark:text-green-400" />
            ) : (
              <UserPlus className="h-6 w-6 text-green-600 dark:text-green-400" />
            )}
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
            {isRegistered ? 'Account Created!' : generatedKey ? 'Your Access Key' : 'Create Account'}
          </CardTitle>
          
        </CardHeader>
        <CardContent>
          {isRegistered ? (
            <div className="space-y-6">
              <div className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-200 dark:border-green-800">
                <div className="text-center mb-4">
                  <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400 mx-auto mb-2" />
                  <div className="text-lg font-semibold text-green-800 dark:text-green-200">Account Created Successfully!</div>
                </div>
                <div className="font-mono text-lg font-bold text-center p-4 bg-white dark:bg-gray-800 rounded-lg border-2 border-green-500 shadow-sm">
                  {generatedKey}
                </div>
                <div className="text-sm text-green-600 dark:text-green-400 mt-3 text-center font-medium">
                  ✅ This key has been saved to your account
                </div>
              </div>

              {showRecoveryCodes && recoveryCodes.length > 0 && (
                <div className="p-6 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-xl border border-amber-200 dark:border-amber-800">
                  <div className="text-center mb-4">
                    <KeyRound className="h-6 w-6 text-amber-600 dark:text-amber-400 mx-auto mb-2" />
                    <div className="text-lg font-semibold text-amber-800 dark:text-amber-200">Recovery Codes</div>
                    <div className="text-sm text-amber-600 dark:text-amber-400 mt-1">
                      If You Forgot Access key, Each can only be used once.
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-3 mb-4">
                    {recoveryCodes.map((code, index) => (
                      <div key={index} className="font-mono text-sm p-3 bg-white dark:bg-gray-800 rounded-lg border text-center shadow-sm">
                        {code}
                      </div>
                    ))}
                  </div>
                  <div className="flex gap-3">
                    <Button onClick={copyRecoveryCodes} variant="outline" size="sm" className="flex-1 border-amber-300 text-amber-700 hover:bg-amber-50 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20">
                      <Copy className="mr-2 h-4 w-4" />
                      Copy Codes
                    </Button>
                    <Button onClick={downloadRecoveryCodes} variant="outline" size="sm" className="flex-1 border-amber-300 text-amber-700 hover:bg-amber-50 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20">
                      <Download className="mr-2 h-4 w-4" />
                      Download
                    </Button>
                  </div>
                </div>
              )}

              <div className="text-sm text-gray-600 dark:text-gray-400 space-y-3 bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                <p className="text-center">🎉 <strong className="text-gray-900 dark:text-white">Welcome to NoteHour!</strong></p>
                
              </div>

              <div className="flex gap-3">
                <Button onClick={copyToClipboard} variant="outline" className="flex-1 border-green-300 text-green-700 hover:bg-green-50 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-900/20">
                  <Copy className="mr-2 h-4 w-4" />
                  Copy Key
                </Button>
                <Button onClick={goToLogin} className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-200">
                  Go to Login
                </Button>
              </div>
            </div>
          ) : generatedKey ? (
            <div className="space-y-6">
              <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                <div className="text-center mb-4">
                  <KeyRound className="h-6 w-6 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                  <div className="text-lg font-semibold text-blue-800 dark:text-blue-200">Generated Access Key</div>
                </div>
                <div className="font-mono text-lg font-bold text-center p-4 bg-white dark:bg-gray-800 rounded-lg border-2 border-blue-500 shadow-sm">
                  {generatedKey}
                </div>
                
              </div>

              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-3 block text-gray-700 dark:text-gray-300">Your Name (Optional)</label>
                  <Input
                    type="text"
                    placeholder="Enter your name"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                    maxLength={50}
                    className="w-full bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400"
                  />
                </div>
              </div>

             

              <div className="flex gap-3">
                <Button
                  onClick={generateNewKey}
                  variant="outline"
                  className="flex-1 border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-700 dark:text-blue-300 dark:hover:bg-blue-900/20"
                  disabled={isLoading || isSaving}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <KeyRound className="mr-2 h-4 w-4" />
                      Generate New Key
                    </>
                  )}
                </Button>
                <Button
                  onClick={saveKey}
                  className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                  disabled={isSaving || isLoading || !generatedKey}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <UserPlus className="mr-2 h-4 w-4" />
                      Save & Create Account
                    </>
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                <UserPlus className="h-8 w-8 text-gray-400 mx-auto mb-3" />
                <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                  <p className="font-medium">Ready to get started?</p>
                 
                </div>
              </div>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium py-3 transition-all duration-200 shadow-lg hover:shadow-xl"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating Key...
                      </>
                    ) : (
                      <>
                        <KeyRound className="mr-2 h-4 w-4" />
                        Generate Access Key
                      </>
                    )}
                  </Button>
                </form>
              </Form>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col items-center gap-3 pt-6">
          {!generatedKey && (
            <>
              <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
                Already have an access key?{' '}
                <Link href="/auth/login" className="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 underline-offset-4 hover:underline transition-colors">
                  Sign In
                </Link>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
                Lost your access key?{' '}
                <Link href="/auth/recovery" className="font-medium text-orange-600 dark:text-orange-400 hover:text-orange-700 dark:hover:text-orange-300 underline-offset-4 hover:underline transition-colors">
                  Recover Account
                </Link>
              </div>
            </>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
