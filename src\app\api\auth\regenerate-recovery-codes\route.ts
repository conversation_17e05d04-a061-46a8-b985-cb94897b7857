import { NextRequest } from 'next/server';
import { authController } from '@/core/controllers';
import { authMiddleware } from '@/app/api/_middleware/auth.middleware';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import { verifyAuth } from '@/utils/auth';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * POST /api/auth/regenerate-recovery-codes - Regenerate user recovery codes
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const authResponse = authMiddleware(req);
    if (authResponse) return authResponse;

    // Get user from token
    const { user } = verifyAuth(req);
    if (!user) {
      return errorResponse('Authentication required', ErrorCode.UNAUTHORIZED, 401);
    }

    // Regenerate recovery codes
    return authController.regenerateRecoveryCodes(user.id);
  } catch (error) {
    logger.error('Regenerate recovery codes error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
