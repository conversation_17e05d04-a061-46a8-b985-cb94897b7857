"use client"

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { profileSchema, ProfileFormValues } from '../schemas';
import { Copy, RefreshCw, Loader2, AlertTriangle, Download, Shield } from 'lucide-react';

interface ProfileFormProps {
  initialData: {
    accessKey: string;
    name?: string;
  };
  onProfileUpdate: (forceRefresh?: boolean) => Promise<void> | void;
}

export function ProfileForm({ initialData, onProfileUpdate }: ProfileFormProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [previewKey, setPreviewKey] = useState<string>('');
  const [showRegenerateDialog, setShowRegenerateDialog] = useState(false);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);
  const [isSavingNewKey, setIsSavingNewKey] = useState(false);
  const [recoveryCodes, setRecoveryCodes] = useState<string[]>([]);
  const [showRecoveryCodesDialog, setShowRecoveryCodesDialog] = useState(false);
  const [isRegeneratingCodes, setIsRegeneratingCodes] = useState(false);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: initialData.name || '',
    },
  });

  // Update form when initialData changes
  useEffect(() => {
    console.log('ProfileForm: initialData changed:', initialData);
    form.reset({
      name: initialData.name || '',
    });
  }, [initialData.name, form]);

  const copyToClipboard = async (key?: string) => {
    try {
      const keyToCopy = key || initialData.accessKey;
      await navigator.clipboard.writeText(keyToCopy);
      toast.success('Access key copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const generateKeyPreview = async () => {
    setIsGeneratingPreview(true);

    try {
      console.log('Generating key preview...');
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: 'generate'
        }),
        credentials: 'include',
      });

      const data = await response.json();
      console.log('Key preview response:', data);

      if (response.ok) {
        // Handle both possible response structures
        const newKey = data.data?.newAccessKey || data.newAccessKey || data.data?.accessKey || data.accessKey;
        if (newKey) {
          setPreviewKey(newKey);
          setShowRegenerateDialog(true);
          toast.success('New key preview generated!');
        } else {
          console.error('No access key in response:', data);
          toast.error('Failed to generate key preview - no key received');
        }
      } else {
        const errorMessage = data.error?.message || data.message || 'Failed to generate key preview';
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Key preview generation error:', error);
      toast.error('Failed to generate key preview');
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  const saveProfile = async (values: ProfileFormValues) => {
    setIsSaving(true);

    try {
      console.log('Saving profile with values:', values);
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: values.name,
        }),
        credentials: 'include',
      });

      const data = await response.json();
      console.log('Profile save response:', data);

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Failed to update profile';
        throw new Error(errorMessage);
      }

      // Refresh profile data and wait for it to complete
      await onProfileUpdate(true);

      toast.success('Profile updated successfully!');
    } catch (error: any) {
      console.error('Profile update error:', error);
      toast.error(error.message || 'Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const savePreviewKey = async () => {
    if (!previewKey) {
      toast.error('No preview key to save');
      return;
    }

    setIsSavingNewKey(true);
    setShowRegenerateDialog(false);

    try {
      console.log('Saving preview key as new access key:', previewKey);

      // Use the regenerate API but with the preview key
      const response = await fetch('/api/auth/regenerate-key', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentAccessKey: initialData.accessKey,
          newAccessKey: previewKey // Pass the preview key to save
        }),
        credentials: 'include',
      });

      const data = await response.json();
      console.log('Save preview key response:', data);

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'Failed to save new access key';
        throw new Error(errorMessage);
      }

      // Refresh profile data to get the new access key
      await onProfileUpdate(true);

      toast.success('New access key saved successfully!');
    } catch (error: any) {
      console.error('Save preview key error:', error);
      toast.error(error.message || 'Failed to save new access key');
    } finally {
      setIsSavingNewKey(false);
      setPreviewKey('');
    }
  };

  const regenerateRecoveryCodes = async () => {
    setIsRegeneratingCodes(true);

    try {
      console.log('Regenerating recovery codes...');
      const response = await fetch('/api/auth/regenerate-recovery-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      const data = await response.json();
      console.log('Recovery codes response:', data);

      if (response.ok) {
        const newCodes = data.data?.recoveryCodes || data.recoveryCodes;
        if (newCodes && Array.isArray(newCodes)) {
          setRecoveryCodes(newCodes);
          setShowRecoveryCodesDialog(true);
          toast.success('Recovery codes regenerated successfully!');
        } else {
          console.error('No recovery codes in response:', data);
          toast.error('Failed to regenerate recovery codes - no codes received');
        }
      } else {
        const errorMessage = data.error?.message || data.message || 'Failed to regenerate recovery codes';
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Error regenerating recovery codes:', error);
      toast.error('Failed to regenerate recovery codes');
    } finally {
      setIsRegeneratingCodes(false);
    }
  };

  const copyRecoveryCodes = async () => {
    try {
      const codesText = recoveryCodes.join('\n');
      await navigator.clipboard.writeText(codesText);
      toast.success('Recovery codes copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const downloadRecoveryCodes = () => {
    if (recoveryCodes.length > 0) {
      const codesText = `NoteHour Recovery Codes\n\nAccess Key: ${initialData.accessKey}\n\nRecovery Codes:\n${recoveryCodes.join('\n')}\n\nIMPORTANT:\n- Save these codes in a secure location\n- Each code can only be used once\n- You'll need these if you lose access to your account\n- Keep them separate from your access key`;

      const blob = new Blob([codesText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `notehour-recovery-codes-${Date.now()}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Recovery codes downloaded!');
    }
  };

  return (
    <div className="space-y-6">
      {/* Profile Information Form */}
      <Card className="border-0 shadow-sm bg-gradient-to-r from-green-50/50 to-emerald-50/50 dark:from-green-950/20 dark:to-emerald-950/20">
        <CardHeader className="pb-4">
          
          
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(saveProfile)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Display Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your name (optional)"
                        {...field}
                        maxLength={50}
                        className="h-10"
                      />
                    </FormControl>
                    <FormMessage />
                   
                  </FormItem>
                )}
              />

              <div className="flex justify-end pt-2">
                <Button
                  type="submit"
                  disabled={isSaving}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Profile'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Access Key Management */}
      <Card className="border-0 shadow-sm bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-blue-800 dark:text-blue-200">
            Access Key
          </CardTitle>
          <CardDescription className="text-blue-600 dark:text-blue-400">
            Your unique access key for authentication
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-900 p-4 rounded-lg border">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-3">
                <label className="text-sm font-medium">Your Access Key</label>
                <div className="flex flex-wrap gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard()}
                    className="h-8 px-3"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy
                  </Button>
                  <Dialog open={showRegenerateDialog} onOpenChange={setShowRegenerateDialog}>
                    <DialogTrigger asChild>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={generateKeyPreview}
                        disabled={isGeneratingPreview}
                        className="h-8 px-3 border-orange-300 hover:bg-orange-100 dark:border-orange-700 dark:hover:bg-orange-900"
                      >
                        {isGeneratingPreview ? (
                          <>
                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <RefreshCw className="h-3 w-3 mr-1" />
                            Regenerate
                          </>
                        )}
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle className="flex items-center gap-2 text-lg">
                          <RefreshCw className="h-5 w-5 text-blue-500" />
                          Regenerate Access Key
                        </DialogTitle>
                        <DialogDescription className="text-sm">
                          Generate a new access key to replace your current one. Make sure to save it safely.
                        </DialogDescription>
                      </DialogHeader>

                      <div className="space-y-4 py-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Current Key:</label>
                          <div className="font-mono text-sm p-3 bg-muted rounded-lg border mt-2 break-all">
                            {initialData.accessKey}
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium text-green-600 dark:text-green-400">New Key Preview:</label>
                          <div className="font-mono text-sm p-3 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800 mt-2 break-all">
                            {previewKey || 'Generating...'}
                          </div>
                          <div className="flex flex-wrap gap-2 mt-3">
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(previewKey)}
                              disabled={!previewKey}
                              className="h-8 px-3 text-xs"
                            >
                              <Copy className="h-3 w-3 mr-1" />
                              Copy
                            </Button>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={generateKeyPreview}
                              disabled={isGeneratingPreview}
                              className="h-8 px-3 text-xs"
                            >
                              {isGeneratingPreview ? (
                                <>
                                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                  Generating...
                                </>
                              ) : (
                                <>
                                  <RefreshCw className="h-3 w-3 mr-1" />
                                  Generate Different
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>

                      <DialogFooter className="flex flex-col sm:flex-row gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setShowRegenerateDialog(false);
                            setPreviewKey('');
                          }}
                          className="w-full sm:w-auto"
                        >
                          Cancel
                        </Button>
                        <Button
                          type="button"
                          onClick={savePreviewKey}
                          disabled={isSavingNewKey || !previewKey}
                          className="bg-green-600 hover:bg-green-700 w-full sm:w-auto"
                        >
                          {isSavingNewKey ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Saving...
                            </>
                          ) : (
                            'Save This Key'
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
              <div className="font-mono text-base p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border break-all">
                {initialData.accessKey}
              </div>
              <p className="text-xs text-muted-foreground mt-2 flex items-start gap-2">
                <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                Keep your access key safe and don't share it with anyone.
              </p>
            </div>

            {/* Recovery Codes Section */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Recovery Codes
                </label>
                <Dialog open={showRecoveryCodesDialog} onOpenChange={setShowRecoveryCodesDialog}>
                  <DialogTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={regenerateRecoveryCodes}
                      disabled={isRegeneratingCodes}
                      className="h-8 px-3 border-orange-300 hover:bg-orange-100 dark:border-orange-700 dark:hover:bg-orange-900"
                    >
                      {isRegeneratingCodes ? (
                        <>
                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="h-3 w-3 mr-1" />
                          Regenerate
                        </>
                      )}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle className="flex items-center gap-2 text-lg">
                        <Shield className="h-5 w-5 text-green-500" />
                        New Recovery Codes Generated
                      </DialogTitle>
                      <DialogDescription className="text-sm">
                        Save these codes securely! Each can only be used once to recover your account.
                      </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4 py-4">
                      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                        <div className="text-sm font-medium mb-2 text-yellow-800 dark:text-yellow-200">🔐 Recovery Codes</div>
                        <div className="grid grid-cols-1 gap-2 mb-3">
                          {recoveryCodes.map((code, index) => (
                            <div key={index} className="font-mono text-sm p-2 bg-background rounded border text-center">
                              {code}
                            </div>
                          ))}
                        </div>
                        <div className="flex flex-wrap gap-2">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={copyRecoveryCodes}
                            disabled={recoveryCodes.length === 0}
                            className="h-8 px-3 text-xs"
                          >
                            <Copy className="h-3 w-3 mr-1" />
                            Copy All
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={downloadRecoveryCodes}
                            disabled={recoveryCodes.length === 0}
                            className="h-8 px-3 text-xs"
                          >
                            <Download className="h-3 w-3 mr-1" />
                            Download
                          </Button>
                        </div>
                      </div>
                    </div>

                    <DialogFooter>
                      <Button
                        type="button"
                        onClick={() => {
                          setShowRecoveryCodesDialog(false);
                          setRecoveryCodes([]);
                        }}
                        className="w-full"
                      >
                        I've Saved These Codes
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border">
                <p className="text-sm text-muted-foreground">
                  Recovery codes allow you to regain access to your account if you lose your access key.
                  Click "Regenerate" to create new recovery codes.
                </p>
              </div>
              <p className="text-xs text-muted-foreground mt-2 flex items-start gap-2">
                <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                Store recovery codes separately from your access key in a secure location.
              </p>
            </div>

            <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-start gap-3">
                <div className="text-xl">🔐</div>
                <div>
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Privacy-focused Authentication</h4>
                  <ul className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                    <li>• No personal information required</li>
                    <li>• Access key is your only identifier</li>
                    <li>• Preview new keys before saving</li>
                    <li>• Recovery codes for account backup</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
