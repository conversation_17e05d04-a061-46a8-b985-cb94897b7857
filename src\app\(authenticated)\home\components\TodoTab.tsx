"use client"

import { Plus, Clock, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { TimeBlock } from '@/lib/types';
import { useState, useEffect } from 'react';
import * as React from 'react';
import { TimeBlockDetails } from '@/components/time-blocks/time-block-grid-component/TimeBlockDetails';
import { TodoGrid } from '@/components/todos/TodoGrid';
import { useTodos } from '@/hooks/use-todos';
import { toast } from 'sonner';

interface TodoTabProps {
  selectedDate: Date;
  onAddBlock: () => void;
  onEditBlock: (block: TimeBlock) => void;
  onDeleteBlock: (id: string) => Promise<boolean>;
  refreshTimeBlocks: () => Promise<void>;
}

export function TodoTab({
  selectedDate,
  onAddBlock,
  onEditBlock,
  onDeleteBlock,
  refreshTimeBlocks,
}: TodoTabProps) {
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [selectedTodo, setSelectedTodo] = useState<TimeBlock | null>(null);

  const {
    todos,
    isLoading,
    toggleTodoComplete,
    deleteTodo,
    convertRoutinesToTodos,
    refresh
  } = useTodos();

  // Combined initialization and date change handling
  React.useEffect(() => {
    let isFirstLoad = true;
    
    const updateTodos = async () => {
      try {
        // Only convert routines if it's first load or date changes
        if (isFirstLoad || selectedDate) {
          await convertRoutinesToTodos(true);
          await refresh();
        }
        isFirstLoad = false;
      } catch (error) {
        console.error('Error updating todos:', error);
      }
    };

    updateTodos();

    // Setup visibility change listener to refresh on tab focus
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        refresh();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [selectedDate, convertRoutinesToTodos, refresh]);

  // Handle todo completion toggle
  const handleToggleComplete = async (todo: TimeBlock) => {
    try {
      // Always ensure isTodo is preserved
      await toggleTodoComplete(todo.id, !todo.isCompleted);
      // Force refresh timeBlocks first, then todos to ensure synchronization
      await refreshTimeBlocks(true);
      await refresh();
    } catch (error) {
      console.error('Error toggling todo completion:', error);
    }
  };

  // Handle todo edit
  const handleEdit = (todo: TimeBlock) => {
    setSelectedTodo(todo);
    onEditBlock(todo);

    // Schedule a refresh after edit completes
    setTimeout(async () => {
      await refreshTimeBlocks(true);
      await refresh();
    }, 2000);
  };

  // Handle todo deletion
  const handleDelete = async (todo: TimeBlock) => {
    if (todo.id) {
      try {
        const success = await onDeleteBlock(todo.id);
        if (success) {
          // Refresh both todos and time blocks to ensure synchronization
          await Promise.all([
            refresh(),
            refreshTimeBlocks()
          ]);
        }
      } catch (error) {
        console.error("Error deleting todo:", error);
        toast.error("Failed to delete todo");
      }
    }
  };

  return (
    <>
      <Card className="col-span-3 lg:col-span-2 flex flex-col h-full">
        <ScrollArea className="flex-1">
          <div className="p-4 h-full">
            {(todos && todos.length > 0) ? (
              <TodoGrid
                todos={todos.filter(todo => todo.isTodo === true)} // Ensure we only show items marked as todos
                onToggleComplete={handleToggleComplete}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            ) : (
              <div className="flex flex-col items-center justify-center p-8 text-center h-full">
                <div className="rounded-full bg-primary/10 p-3 mb-3">
                  <Clock className="h-6 w-6 text-primary" />
                </div>
                <p className="text-base font-medium mb-2">No todos yet</p>
                <p className="text-muted-foreground mb-4 max-w-md text-sm">
                  Add a todo item or create routines to automatically generate todos.
                </p>
                <div className="flex gap-3 flex-wrap justify-center">
                  <Button
                    onClick={() => {
                      onAddBlock();
                      // Schedule a refresh after adding new todo
                      setTimeout(() => {
                        refresh();
                      }, 2000);
                    }}
                    size="sm"
                    variant="default"
                    className="rounded-full px-4 h-9 shadow-sm"
                  >
                    <Plus className="h-3.5 w-3.5 mr-2" />
                    Add Todo
                  </Button>
                  <Button
                    onClick={() => window.location.href = '/routine'}
                    size="sm"
                    variant="outline"
                    className="border border-primary/30 hover:bg-primary/10 text-primary rounded-full px-4 h-9 shadow-sm"
                  >
                    <Clock className="h-3.5 w-3.5 mr-2" />
                    Manage Routines
                  </Button>
                  <Button
                    onClick={async () => {
                      // Force conversion when explicitly requested by user
                      await convertRoutinesToTodos(true);
                      // Explicitly refresh to ensure UI updates
                      await refresh();
                    }}
                    size="sm"
                    variant="outline"
                    className="border border-primary/30 hover:bg-primary/10 text-primary rounded-full px-4 h-9 shadow-sm"
                  >
                    <Clock className="h-3.5 w-3.5 mr-2" />
                    Sync Routines to Todos
                  </Button>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </Card>

      {selectedTodo && (
        <TimeBlockDetails
          isOpen={isDetailsOpen}
          onClose={() => setIsDetailsOpen(false)}
          timeBlock={selectedTodo}
          date={selectedDate}
          onEdit={onEditBlock}
          onDelete={async (timeBlock) => {
            if (timeBlock && timeBlock.id) {
              try {
                const success = await onDeleteBlock(timeBlock.id);
                if (success) {
                  // Refresh the todos hook to ensure UI updates
                  refresh();
                }
                return success;
              } catch (error) {
                console.error("Error deleting todo from details:", error);
                toast.error("Failed to delete todo");
                return false;
              }
            }
            return Promise.resolve(false);
          }}
        />
      )}
    </>
  );
}
