"use client"

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, LayoutDashboard, Loader2, KeyRound, Shield } from 'lucide-react';
import { toast } from 'sonner';
import { Alert, AlertDescription } from '@/components/ui/alert';

const recoverySchema = z.object({
  accessKey: z.string()
    .min(1, { message: 'Access key is required' })
    .refine((key) => {
      // New format: 8 words + 10 digits
      const newFormat = /^[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-\d{10}$/.test(key);
      // Legacy format: 2 words + 2 digits
      const legacyFormat = /^[a-z]+-[a-z]+-\d{2}$/.test(key);
      return newFormat || legacyFormat;
    }, 'Invalid access key format. Expected: 8-words-10digits or legacy 2-words-2digits'),
  recoveryCode: z.string()
    .min(1, { message: 'Recovery code is required' })
    .regex(/^[A-F0-9]{8}$/, { message: 'Invalid recovery code format. Expected: 8 uppercase alphanumeric characters' }),
});

export default function RecoveryPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const router = useRouter();

  const form = useForm<z.infer<typeof recoverySchema>>({
    resolver: zodResolver(recoverySchema),
    defaultValues: {
      accessKey: '',
      recoveryCode: '',
    },
  });

  async function onSubmit(values: z.infer<typeof recoverySchema>) {
    setIsLoading(true);
    setErrorDetails(null);

    try {
      const requestBody = {
        accessKey: values.accessKey.toLowerCase().trim(),
        recoveryCode: values.recoveryCode.toUpperCase().trim(),
      };

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        credentials: 'include',
      });

      const responseText = await response.text();

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        setErrorDetails('Invalid response from server. Please try again later.');
        throw new Error('Server returned an invalid response');
      }

      if (!response.ok || !data.success) {
        const errorMessage = data.error?.message || 'Unknown error';
        const errorCode = data.error?.code || 'UNKNOWN_ERROR';

        if (errorCode === 'INVALID_CREDENTIALS' || errorCode === 'USER_NOT_FOUND') {
          setErrorDetails('Invalid access key or recovery code. Please check your credentials and try again.');
          throw new Error('Invalid credentials');
        } else if (errorCode === 'RECOVERY_CODE_USED') {
          setErrorDetails('This recovery code has already been used. Please try a different recovery code.');
          throw new Error('Recovery code already used');
        } else if (errorCode === 'DATABASE_ERROR') {
          setErrorDetails('Database connection error. Please try again later.');
          throw new Error('Database connection failed');
        } else {
          setErrorDetails(`Recovery failed: ${errorMessage}`);
          throw new Error(errorMessage || 'Recovery failed');
        }
      }

      const responseData = data.data || {};
      const token = responseData.token;
      const user = responseData.user;

      if (!token || !user) {
        setErrorDetails('Server response is missing required data');
        throw new Error('Invalid server response');
      }

      // Store in localStorage for PWA support and offline access
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify({
        id: user.id,
        name: user.name,
        email: user.email,
        lastLogin: new Date().toISOString()
      }));

      localStorage.setItem('userProfile', JSON.stringify(user));

      // Clear categories cache so next page fetches fresh categories
      localStorage.removeItem('categories');

      toast.success('Account recovered successfully! You are now logged in.');
      router.push('/home');
    } catch (error: any) {
      toast.error(error.message || 'Account recovery failed');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <Link href="/" className="absolute left-4 top-4 md:left-8 md:top-8 flex items-center gap-2 font-bold text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">
        <LayoutDashboard className="h-5 w-5" />
        <span>NoteHour</span>
      </Link>

      <Card className="w-full max-w-md shadow-xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
        <CardHeader className="text-center pb-6">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/30">
            <Shield className="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
            Recover Your Account
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-400 mt-2">
            Use your access key and a recovery code to regain access to your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {errorDetails && (
                <Alert variant="destructive" className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="mt-2">
                    <div className="text-sm font-medium">Recovery Error:</div>
                    <div className="text-xs mt-1 break-words">{errorDetails}</div>
                  </AlertDescription>
                </Alert>
              )}

              <FormField
                control={form.control}
                name="accessKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700 dark:text-gray-300 font-medium">
                      Access Key
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <KeyRound className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="your-access-key-here"
                          {...field}
                          className="pl-10 font-mono text-sm bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400"
                          autoComplete="off"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="recoveryCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700 dark:text-gray-300 font-medium">
                      Recovery Code
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Shield className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="A1B2C3D4"
                          {...field}
                          className="pl-10 font-mono text-sm bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 uppercase"
                          autoComplete="off"
                          maxLength={8}
                          onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
                <div className="text-sm text-amber-800 dark:text-amber-200">
                  <div className="font-medium mb-2">⚠️ Important Notes:</div>
                  <ul className="text-xs space-y-1 list-disc list-inside">
                    <li>Each recovery code can only be used once</li>
                    <li>You have 5 recovery codes total</li>
                    <li>After using this code, it will be permanently disabled</li>
                    <li>Make sure to save your remaining recovery codes</li>
                  </ul>
                </div>
              </div>

              <Button 
                type="submit" 
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-2.5 transition-all duration-200 shadow-lg hover:shadow-xl" 
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Recovering Account...
                  </>
                ) : (
                  <>
                    <Shield className="mr-2 h-4 w-4" />
                    Recover Account
                  </>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col items-center gap-3 pt-6">
          <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
            Remember your access key?{' '}
            <Link href="/auth/login" className="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 underline-offset-4 hover:underline transition-colors">
              Sign In
            </Link>
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
            Need a new account?{' '}
            <Link href="/auth/register" className="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 underline-offset-4 hover:underline transition-colors">
              Generate Access Key
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
